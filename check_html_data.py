# 检查HTML中的数据总和
data_a = [0, 0, 0, 1, 4, 1, 1, 1, 0, 0, 1, 4, 0, 2, 0, 0, 1, 1, 2, 0, 2, 1, 0, 1, 0, 3, 0, 2, 0, 0, 0]
data_b = [7, 4, 2, 6, 7, 7, 8, 7, 7, 1, 7, 8, 5, 5, 3, 5, 0, 7, 9, 5, 6, 9, 8, 0, 7, 5, 5, 6, 5, 5, 5]
data_c = [4, 2, 0, 6, 5, 4, 3, 3, 0, 0, 6, 5, 2, 2, 2, 2, 0, 2, 5, 3, 2, 3, 3, 0, 3, 8, 3, 2, 3, 3, 0]

total_a = sum(data_a)
total_b = sum(data_b)
total_c = sum(data_c)

print(f'HTML中A栋总数: {total_a}')
print(f'HTML中B栋总数: {total_b}')
print(f'HTML中C栋总数: {total_c}')
print(f'HTML中总计: {total_a + total_b + total_c}')

print('\n应该的数据:')
print('A栋: 28')
print('B栋: 171')
print('C栋: 86')
print('总计: 285')

print('\n差异:')
print(f'A栋差异: {28 - total_a}')
print(f'B栋差异: {171 - total_b}')
print(f'C栋差异: {86 - total_c}')
