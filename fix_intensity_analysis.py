import pandas as pd
import re
import json

def analyze_work_intensity():
    """重新分析作业强度数据"""
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    print("=== 作业强度重新分析 ===")
    
    # 按日期统计作业数量
    daily_counts = df.groupby('日期').size().reset_index(name='作业数量')
    daily_counts = daily_counts.sort_values('作业数量', ascending=False)
    
    print("每日作业数量统计:")
    print(daily_counts)
    
    # 计算关键指标
    max_daily_tasks = daily_counts['作业数量'].max()
    min_daily_tasks = daily_counts['作业数量'].min()
    avg_daily_tasks = daily_counts['作业数量'].mean()
    total_tasks = len(df)
    working_days = len(daily_counts)
    
    # 超时数据（之前已分析过）
    def is_overtime(start_time_str):
        if pd.isna(start_time_str):
            return False
        try:
            time_str = str(start_time_str).strip()
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])
            from datetime import datetime, time
            start_time = datetime.strptime(time_str, '%H:%M').time()
            return start_time > time(17, 30)
        except:
            return False
    
    df['是否超时'] = df['起始时间'].apply(is_overtime)
    overtime_tasks = df['是否超时'].sum()
    overtime_rate = (overtime_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    print(f"\n=== 正确的统计数据 ===")
    print(f"总作业数: {total_tasks}")
    print(f"工作天数: {working_days}")
    print(f"最高强度: {max_daily_tasks}个任务/天")
    print(f"最低强度: {min_daily_tasks}个任务/天")
    print(f"平均强度: {avg_daily_tasks:.1f}个任务/天")
    print(f"超时作业数: {overtime_tasks}")
    print(f"超时率: {overtime_rate:.1f}%")
    
    # 找出最高强度的日期
    max_day = daily_counts.iloc[0]
    print(f"最高强度日期: {max_day['日期']} ({max_day['作业数量']}个任务)")
    
    return {
        'total_tasks': total_tasks,
        'working_days': working_days,
        'max_daily_tasks': int(max_daily_tasks),
        'min_daily_tasks': int(min_daily_tasks),
        'avg_daily_tasks': round(avg_daily_tasks, 1),
        'overtime_tasks': int(overtime_tasks),
        'overtime_rate': round(overtime_rate, 1),
        'max_day': max_day['日期'],
        'daily_counts': daily_counts
    }

def update_html_statistics(stats):
    """更新HTML报告中的所有统计数据"""
    
    print(f"\n=== 更新HTML报告统计数据 ===")
    
    # 读取HTML文件
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 更新作业强度分析文本
    intensity_text = f"从热力图可以看出，作业分布相对均匀，最高强度达到{stats['max_daily_tasks']}个任务/天，超时作业比例{stats['overtime_rate']}%，平均每日作业量{stats['avg_daily_tasks']}个任务，作业负荷管理良好。"
    
    # 查找并替换作业强度分析文本
    pattern = r'从热力图可以看出，作业分布相对均匀，最高强度达到\d+个任务/天，超时作业比例[\d.]+%，平均每日作业量[\d.]+个任务，作业负荷管理良好。'
    html_content = re.sub(pattern, intensity_text, html_content)
    
    # 如果上面的模式没有匹配到，尝试更宽泛的模式
    if f"{stats['max_daily_tasks']}个任务/天" not in html_content:
        # 查找包含"个任务/天"的文本并替换
        pattern2 = r'最高强度达到\d+个任务/天'
        html_content = re.sub(pattern2, f'最高强度达到{stats["max_daily_tasks"]}个任务/天', html_content)
        
        pattern3 = r'超时作业比例[\d.]+%'
        html_content = re.sub(pattern3, f'超时作业比例{stats["overtime_rate"]}%', html_content)
        
        pattern4 = r'平均每日作业量[\d.]+个任务'
        html_content = re.sub(pattern4, f'平均每日作业量{stats["avg_daily_tasks"]}个任务', html_content)
    
    # 更新JavaScript中的统计数据
    html_content = re.sub(r'totalTasks: \d+', f'totalTasks: {stats["total_tasks"]}', html_content)
    html_content = re.sub(r'overtimeTasks: \d+', f'overtimeTasks: {stats["overtime_tasks"]}', html_content)
    html_content = re.sub(r'overtimeRate: [\d.]+', f'overtimeRate: {stats["overtime_rate"]}', html_content)
    html_content = re.sub(r'workingDays: \d+', f'workingDays: {stats["working_days"]}', html_content)
    html_content = re.sub(r'maxDailyTasks: \d+', f'maxDailyTasks: {stats["max_daily_tasks"]}', html_content)
    html_content = re.sub(r'avgDailyTasks: [\d.]+', f'avgDailyTasks: {stats["avg_daily_tasks"]}', html_content)
    
    # 更新HTML中显示的统计卡片
    html_content = re.sub(r'<div class="stat-number">\d+</div>\s*<div class="stat-label">总作业任务数</div>', 
                         f'<div class="stat-number">{stats["total_tasks"]}</div>\n                <div class="stat-label">总作业任务数</div>', 
                         html_content)
    
    html_content = re.sub(r'<div class="stat-number">\d+</div>\s*<div class="stat-label">超时作业任务数</div>', 
                         f'<div class="stat-number">{stats["overtime_tasks"]}</div>\n                <div class="stat-label">超时作业任务数</div>', 
                         html_content)
    
    html_content = re.sub(r'<div class="stat-number">[\d.]+%</div>\s*<div class="stat-label">超时作业比例</div>', 
                         f'<div class="stat-number">{stats["overtime_rate"]}%</div>\n                <div class="stat-label">超时作业比例</div>', 
                         html_content)
    
    html_content = re.sub(r'<div class="stat-number">\d+</div>\s*<div class="stat-label">作业天数</div>', 
                         f'<div class="stat-number">{stats["working_days"]}</div>\n                <div class="stat-label">作业天数</div>', 
                         html_content)
    
    # 保存更新后的HTML文件
    with open('8月份作业日历热力图与超时分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✓ HTML报告已更新")
    print(f"  - 总作业任务数: {stats['total_tasks']}")
    print(f"  - 超时作业任务数: {stats['overtime_tasks']}")
    print(f"  - 超时作业比例: {stats['overtime_rate']}%")
    print(f"  - 最高强度: {stats['max_daily_tasks']}个任务/天")
    print(f"  - 平均强度: {stats['avg_daily_tasks']}个任务/天")
    print(f"  - 工作天数: {stats['working_days']}")

def verify_updates(stats):
    """验证更新结果"""
    
    print(f"\n=== 验证更新结果 ===")
    
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 检查关键数据是否正确更新
    checks = [
        (f'{stats["max_daily_tasks"]}个任务/天', "最高强度"),
        (f'{stats["overtime_rate"]}%', "超时率"),
        (f'{stats["avg_daily_tasks"]}个任务', "平均强度"),
        (f'totalTasks: {stats["total_tasks"]}', "JavaScript总任务数"),
        (f'maxDailyTasks: {stats["max_daily_tasks"]}', "JavaScript最大日任务数")
    ]
    
    for check_text, description in checks:
        if check_text in html_content:
            print(f"  ✓ {description}: {check_text}")
        else:
            print(f"  ✗ {description}: 未找到 {check_text}")

def main():
    print("开始重新分析作业强度数据...")
    
    # 分析数据
    stats = analyze_work_intensity()
    
    # 更新HTML报告
    update_html_statistics(stats)
    
    # 验证更新
    verify_updates(stats)
    
    print(f"\n=== 修正完成 ===")
    print(f"主要修正:")
    print(f"  • 最高强度: {stats['max_daily_tasks']}个任务/天 (发生在{stats['max_day']})")
    print(f"  • 平均强度: {stats['avg_daily_tasks']}个任务/天")
    print(f"  • 超时率: {stats['overtime_rate']}%")
    print(f"  • 工作天数: {stats['working_days']}天")

if __name__ == "__main__":
    main()
