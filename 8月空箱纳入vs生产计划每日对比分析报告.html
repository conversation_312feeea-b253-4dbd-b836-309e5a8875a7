<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8月空箱纳入vs生产计划每日对比分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .chart-container {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .model-selector {
            margin-bottom: 20px;
        }
        .model-selector select {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>8月空箱纳入vs生产计划每日对比分析报告</h1>
            <p>基于作业登记表ABC栋20250801~20250831_自动排程.xlsx数据 | 分析时间范围: 2025年8月1日-31日</p>
        </div>

        <div class="summary-grid" id="summaryGrid">
            <!-- 概览卡片将通过JavaScript生成 -->
        </div>

        <div class="chart-container">
            <div class="model-selector">
                <label for="modelSelect">选择机种: </label>
                <select id="modelSelect" onchange="updateChart()">
                    <!-- 选项将通过JavaScript生成 -->
                </select>
            </div>
            <canvas id="comparisonChart" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        // 数据
        const emptyBoxAnalysisData = {
            "JT026": {
                        "dates": [
                                    "08-01",
                                    "08-02",
                                    "08-03",
                                    "08-04",
                                    "08-05",
                                    "08-06",
                                    "08-07",
                                    "08-08",
                                    "08-09",
                                    "08-10",
                                    "08-11",
                                    "08-12",
                                    "08-13",
                                    "08-14",
                                    "08-15",
                                    "08-16",
                                    "08-17",
                                    "08-18",
                                    "08-19",
                                    "08-20",
                                    "08-21",
                                    "08-22",
                                    "08-23",
                                    "08-24",
                                    "08-25",
                                    "08-26",
                                    "08-27",
                                    "08-28",
                                    "08-29",
                                    "08-30",
                                    "08-31"
                        ],
                        "plan": [
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.2,
                                    0.1,
                                    0.2,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.2,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.2,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.2
                        ],
                        "empty_box": [
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1
                        ],
                        "working_days": 25,
                        "plan_total": 3.0,
                        "empty_total": 1.2
            },
            "JT028": {
                        "dates": [
                                    "08-01",
                                    "08-02",
                                    "08-03",
                                    "08-04",
                                    "08-05",
                                    "08-06",
                                    "08-07",
                                    "08-08",
                                    "08-09",
                                    "08-10",
                                    "08-11",
                                    "08-12",
                                    "08-13",
                                    "08-14",
                                    "08-15",
                                    "08-16",
                                    "08-17",
                                    "08-18",
                                    "08-19",
                                    "08-20",
                                    "08-21",
                                    "08-22",
                                    "08-23",
                                    "08-24",
                                    "08-25",
                                    "08-26",
                                    "08-27",
                                    "08-28",
                                    "08-29",
                                    "08-30",
                                    "08-31"
                        ],
                        "plan": [
                                    0.2,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.2,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0
                        ],
                        "empty_box": [
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0
                        ],
                        "working_days": 24,
                        "plan_total": 2.6,
                        "empty_total": 1.0
            },
            "JH027": {
                        "dates": [
                                    "08-01",
                                    "08-02",
                                    "08-03",
                                    "08-04",
                                    "08-05",
                                    "08-06",
                                    "08-07",
                                    "08-08",
                                    "08-09",
                                    "08-10",
                                    "08-11",
                                    "08-12",
                                    "08-13",
                                    "08-14",
                                    "08-15",
                                    "08-16",
                                    "08-17",
                                    "08-18",
                                    "08-19",
                                    "08-20",
                                    "08-21",
                                    "08-22",
                                    "08-23",
                                    "08-24",
                                    "08-25",
                                    "08-26",
                                    "08-27",
                                    "08-28",
                                    "08-29",
                                    "08-30",
                                    "08-31"
                        ],
                        "plan": [
                                    0.2,
                                    0.0,
                                    0.1,
                                    0.2,
                                    0.2,
                                    0.2,
                                    0.1,
                                    0.2,
                                    0.0,
                                    0.0,
                                    0.3,
                                    0.2,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.2,
                                    0.3,
                                    0.1,
                                    0.2,
                                    0.2,
                                    0.1,
                                    0.0,
                                    0.2,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0
                        ],
                        "empty_box": [
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.1,
                                    0.2,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.0,
                                    0.1,
                                    0.0
                        ],
                        "working_days": 23,
                        "plan_total": 3.7,
                        "empty_total": 1.7
            }
};
        
        let comparisonChart = null;

        // 生成概览卡片
        function generateSummaryCards() {
            const summaryGrid = document.getElementById('summaryGrid');
            summaryGrid.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {
                const data = emptyBoxAnalysisData[model];
                
                const card = document.createElement('div');
                card.className = 'summary-card';
                card.innerHTML = `
                    <h3>${model}</h3>
                    <div class="value">${data.working_days}天</div>
                    <div>工作天数</div>
                    <div style="margin-top: 10px;">
                        <div>计划总量: ${data.plan_total}K</div>
                        <div>空箱总量: ${data.empty_total}K</div>
                    </div>
                `;
                summaryGrid.appendChild(card);
            });
        }

        // 填充机种选择器
        function populateModelSelector() {
            const select = document.getElementById('modelSelect');
            select.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });
        }

        // 更新图表
        function updateChart() {
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel || !emptyBoxAnalysisData[selectedModel]) return;

            const data = emptyBoxAnalysisData[selectedModel];
            const ctx = document.getElementById('comparisonChart').getContext('2d');

            if (comparisonChart) {
                comparisonChart.destroy();
            }

            comparisonChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates,
                    datasets: [{
                        label: '生产计划 (K件)',
                        data: data.plan,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.1
                    }, {
                        label: '空箱纳入 (K件)',
                        data: data.empty_box,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: `${selectedModel} - 每日计划vs空箱纳入对比`
                        },
                        legend: {
                            display: true
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量 (千件)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，Chart.js版本:', Chart.version);
            generateSummaryCards();
            populateModelSelector();
            updateChart();
        });

        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.error);
        });
    </script>
</body>
</html>