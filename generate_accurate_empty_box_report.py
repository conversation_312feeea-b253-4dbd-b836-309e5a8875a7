import pandas as pd
import json
import re
from datetime import datetime

def analyze_empty_box_vs_production():
    """基于Excel数据生成准确的空箱纳入vs生产计划对比分析"""
    
    print("=== 空箱纳入vs生产计划分析 ===")
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    print(f"总记录数: {len(df)}")
    print(f"日期范围: {df['日期'].min()} 到 {df['日期'].max()}")
    print(f"作业类型: {df['作业类型'].unique()}")
    
    # 提取机种信息
    def extract_model(content):
        if pd.isna(content):
            return "其他"
        content_str = str(content)
        # 匹配常见机种模式
        patterns = [
            r'(JT\d{3})',  # JT026, JT028等
            r'(JH\d{3})',  # JH027等
            r'(UF\d{3})',  # UF009等
            r'(US\d{3})',  # US001等
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content_str)
            if match:
                return match.group(1)
        
        # 如果没有匹配到标准模式，取前几个字符
        if len(content_str) >= 5:
            return content_str[:5]
        return "其他"
    
    df['机种'] = df['作业内容'].apply(extract_model)
    
    # 统计机种分布
    model_counts = df['机种'].value_counts()
    print(f"\n机种分布:")
    for model, count in model_counts.head(10).items():
        print(f"  {model}: {count}条记录")
    
    # 分析空箱纳入（入库）vs 生产计划（出库）
    print(f"\n作业类型分析:")
    type_counts = df['作业类型'].value_counts()
    for work_type, count in type_counts.items():
        print(f"  {work_type}: {count}条记录")
    
    # 选择主要机种进行分析
    main_models = model_counts.head(6).index.tolist()
    print(f"\n主要机种: {main_models}")
    
    # 生成每日对比数据
    analysis_data = {}
    
    for model in main_models:
        if model == "其他":
            continue
            
        model_df = df[df['机种'] == model]
        
        # 按日期统计
        daily_data = {
            "dates": [],
            "plan": [],      # 生产计划（出库）
            "empty_box": [], # 空箱纳入（入库）
            "working_days": 0,
            "plan_total": 0,
            "empty_total": 0
        }
        
        # 生成8月1-31日的数据
        for day in range(1, 32):
            date_str = f"8/{day}"
            daily_data["dates"].append(f"08-{day:02d}")
            
            day_data = model_df[model_df['日期'] == date_str]
            
            # 生产计划数量（出库作业）
            plan_data = day_data[day_data['作业类型'] == '出库']
            plan_qty = plan_data['数量'].sum() / 1000  # 转换为千件
            daily_data["plan"].append(round(plan_qty, 1))
            
            # 空箱纳入数量（入库作业）
            empty_data = day_data[day_data['作业类型'] == '入库']
            empty_qty = empty_data['数量'].sum() / 1000  # 转换为千件
            daily_data["empty_box"].append(round(empty_qty, 1))
        
        # 计算汇总数据
        daily_data["working_days"] = sum(1 for x in daily_data["plan"] + daily_data["empty_box"] if x > 0)
        daily_data["plan_total"] = round(sum(daily_data["plan"]), 1)
        daily_data["empty_total"] = round(sum(daily_data["empty_box"]), 1)
        
        analysis_data[model] = daily_data
        
        print(f"\n{model} 数据:")
        print(f"  工作天数: {daily_data['working_days']}")
        print(f"  生产计划总量: {daily_data['plan_total']}K件")
        print(f"  空箱纳入总量: {daily_data['empty_total']}K件")
    
    return analysis_data

def generate_html_report(analysis_data):
    """生成HTML报告"""
    
    print(f"\n=== 生成HTML报告 ===")
    
    # 计算总体统计
    total_models = len(analysis_data)
    total_plan = sum(data['plan_total'] for data in analysis_data.values())
    total_empty = sum(data['empty_total'] for data in analysis_data.values())
    total_working_days = max(data['working_days'] for data in analysis_data.values()) if analysis_data else 0
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8月空箱纳入vs生产计划每日对比分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }}
        .header p {{
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 14px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: normal;
        }}
        .summary-card .value {{
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .summary-card .unit {{
            font-size: 14px;
            opacity: 0.9;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 25px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }}
        .model-selector {{
            margin-bottom: 20px;
            text-align: center;
        }}
        .model-selector label {{
            font-weight: bold;
            margin-right: 10px;
            color: #2c3e50;
        }}
        .model-selector select {{
            padding: 10px 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: white;
            color: #2c3e50;
            cursor: pointer;
            transition: border-color 0.3s;
        }}
        .model-selector select:focus {{
            outline: none;
            border-color: #667eea;
        }}
        .analysis-section {{
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }}
        .analysis-section h3 {{
            color: #2c3e50;
            margin-top: 0;
        }}
        .analysis-section ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        .analysis-section li {{
            margin: 8px 0;
            color: #34495e;
        }}
        .highlight {{
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>8月空箱纳入vs生产计划每日对比分析报告</h1>
            <p>基于作业登记表ABC栋20250801~20250831_自动排程.xlsx数据 | 分析时间范围: 2025年8月1日-31日</p>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>分析机种数</h3>
                <div class="value">{total_models}</div>
                <div class="unit">个机种</div>
            </div>
            <div class="summary-card">
                <h3>生产计划总量</h3>
                <div class="value">{total_plan}</div>
                <div class="unit">千件</div>
            </div>
            <div class="summary-card">
                <h3>空箱纳入总量</h3>
                <div class="value">{total_empty}</div>
                <div class="unit">千件</div>
            </div>
            <div class="summary-card">
                <h3>分析天数</h3>
                <div class="value">31</div>
                <div class="unit">天</div>
            </div>
        </div>

        <div class="chart-container">
            <div class="model-selector">
                <label for="modelSelect">选择机种: </label>
                <select id="modelSelect" onchange="updateChart()">
                    <!-- 选项将通过JavaScript生成 -->
                </select>
            </div>
            <canvas id="comparisonChart" width="400" height="200"></canvas>
        </div>

        <div class="analysis-section">
            <h3>📊 分析总结</h3>
            <ul>
                <li><strong>数据来源</strong>：基于作业登记表ABC栋实际作业数据分析</li>
                <li><strong>分析维度</strong>：按机种分类，对比空箱纳入（入库）与生产计划（出库）</li>
                <li><strong>时间范围</strong>：2025年8月1日-31日，共31天</li>
                <li><strong>数据单位</strong>：千件（K件），便于图表显示和对比</li>
            </ul>
        </div>

        <div class="analysis-section">
            <h3>🔍 关键发现</h3>
            <ul>
                <li><strong>机种分布</strong>：主要分析{total_models}个机种的作业情况</li>
                <li><strong>作业平衡</strong>：生产计划总量{total_plan}K件，空箱纳入总量{total_empty}K件</li>
                <li><strong>数据基础</strong>：基于实际作业登记数据，确保分析准确性</li>
                <li><strong>可视化展示</strong>：支持按机种切换查看详细对比趋势</li>
            </ul>
        </div>
    </div>

    <script>
        // 数据
        const emptyBoxAnalysisData = {json.dumps(analysis_data, ensure_ascii=False, indent=12)};
        
        let comparisonChart = null;

        // 填充机种选择器
        function populateModelSelector() {{
            const select = document.getElementById('modelSelect');
            select.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {{
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            }});
        }}

        // 更新图表
        function updateChart() {{
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel || !emptyBoxAnalysisData[selectedModel]) return;

            const data = emptyBoxAnalysisData[selectedModel];
            const ctx = document.getElementById('comparisonChart').getContext('2d');

            if (comparisonChart) {{
                comparisonChart.destroy();
            }}

            comparisonChart = new Chart(ctx, {{
                type: 'line',
                data: {{
                    labels: data.dates,
                    datasets: [{{
                        label: '生产计划 (K件)',
                        data: data.plan,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }}, {{
                        label: '空箱纳入 (K件)',
                        data: data.empty_box,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }}]
                }},
                options: {{
                    responsive: true,
                    plugins: {{
                        title: {{
                            display: true,
                            text: `${{selectedModel}} - 空箱纳入vs生产计划每日对比`,
                            font: {{
                                size: 16,
                                weight: 'bold'
                            }}
                        }},
                        legend: {{
                            display: true,
                            position: 'top'
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '数量 (千件)',
                                font: {{
                                    weight: 'bold'
                                }}
                            }},
                            grid: {{
                                color: 'rgba(0,0,0,0.1)'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: '日期 (8月)',
                                font: {{
                                    weight: 'bold'
                                }}
                            }},
                            grid: {{
                                color: 'rgba(0,0,0,0.1)'
                            }}
                        }}
                    }},
                    interaction: {{
                        intersect: false,
                        mode: 'index'
                    }}
                }}
            }});
        }}

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('页面加载完成，Chart.js版本:', Chart.version);
            populateModelSelector();
            if (Object.keys(emptyBoxAnalysisData).length > 0) {{
                updateChart();
            }}
        }});

        // 错误处理
        window.addEventListener('error', function(e) {{
            console.error('JavaScript错误:', e.error);
        }});
    </script>
</body>
</html>'''

    # 保存HTML文件
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✓ HTML报告已生成")
    print(f"  - 包含机种: {list(analysis_data.keys())}")
    print(f"  - 生产计划总量: {total_plan}K件")
    print(f"  - 空箱纳入总量: {total_empty}K件")

def main():
    print("开始生成准确的空箱纳入vs生产计划对比分析报告...")
    
    # 分析数据
    analysis_data = analyze_empty_box_vs_production()
    
    # 生成HTML报告
    generate_html_report(analysis_data)
    
    print(f"\n✅ 报告生成完成！")
    print(f"文件名: 8月空箱纳入vs生产计划每日对比分析报告.html")
    print(f"数据来源: 作业登记表ABC栋20250801~20250831_自动排程.xlsx")

if __name__ == "__main__":
    main()
