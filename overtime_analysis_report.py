import pandas as pd
from datetime import datetime, time

def generate_detailed_overtime_report():
    """生成详细的超时分析报告"""
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    # 判断超时函数
    def is_overtime(start_time_str):
        if pd.isna(start_time_str):
            return False
        try:
            time_str = str(start_time_str).strip()
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])
            start_time = datetime.strptime(time_str, '%H:%M').time()
            return start_time > time(17, 30)
        except:
            return False
    
    df['是否超时'] = df['起始时间'].apply(is_overtime)
    
    print("=" * 60)
    print("📊 8月份作业超时分析详细报告")
    print("=" * 60)
    print(f"📅 分析期间: 2025年8月1日 - 8月31日")
    print(f"⏰ 超时标准: 开始作业时间 > 17:30")
    print(f"📋 数据来源: 作业登记表ABC栋20250801~20250831_自动排程.xlsx")
    
    # 总体统计
    total_tasks = len(df)
    overtime_tasks = df['是否超时'].sum()
    overtime_rate = (overtime_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    print(f"\n📈 总体统计:")
    print(f"  • 总作业数: {total_tasks} 个")
    print(f"  • 超时作业数: {overtime_tasks} 个")
    print(f"  • 超时率: {overtime_rate:.1f}%")
    
    # 按栋别分析
    print(f"\n🏢 各栋别超时情况:")
    building_stats = df.groupby('栋别').agg({
        '是否超时': ['count', 'sum']
    })
    building_stats.columns = ['总作业数', '超时作业数']
    building_stats['超时率%'] = (building_stats['超时作业数'] / building_stats['总作业数'] * 100).round(1)
    
    for building in ['A', 'B', 'C']:
        if building in building_stats.index:
            stats = building_stats.loc[building]
            print(f"  • {building}栋: {stats['超时作业数']}/{stats['总作业数']} ({stats['超时率%']}%)")
    
    # 按作业类型分析
    print(f"\n📦 各作业类型超时情况:")
    type_stats = df.groupby('作业类型').agg({
        '是否超时': ['count', 'sum']
    })
    type_stats.columns = ['总作业数', '超时作业数']
    type_stats['超时率%'] = (type_stats['超时作业数'] / type_stats['总作业数'] * 100).round(1)
    
    for work_type in type_stats.index:
        stats = type_stats.loc[work_type]
        print(f"  • {work_type}: {stats['超时作业数']}/{stats['总作业数']} ({stats['超时率%']}%)")
    
    # 超时高峰日期
    print(f"\n📅 超时高峰日期:")
    daily_overtime = df.groupby('日期')['是否超时'].sum().sort_values(ascending=False)
    top_overtime_days = daily_overtime[daily_overtime > 0].head(5)
    
    for date, count in top_overtime_days.items():
        day_total = len(df[df['日期'] == date])
        rate = (count / day_total * 100) if day_total > 0 else 0
        print(f"  • {date}: {count}个超时作业 (当日总作业{day_total}个, 超时率{rate:.1f}%)")
    
    # 超时时间段分析
    print(f"\n⏰ 超时时间段分析:")
    overtime_jobs = df[df['是否超时'] == True]
    
    time_ranges = {
        '17:31-18:30': 0,
        '18:31-19:30': 0,
        '19:31-20:30': 0,
        '20:31-21:30': 0,
        '21:31以后': 0
    }
    
    for _, job in overtime_jobs.iterrows():
        try:
            time_str = str(job['起始时间']).strip()
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])
            start_time = datetime.strptime(time_str, '%H:%M').time()
            
            if time(17, 31) <= start_time <= time(18, 30):
                time_ranges['17:31-18:30'] += 1
            elif time(18, 31) <= start_time <= time(19, 30):
                time_ranges['18:31-19:30'] += 1
            elif time(19, 31) <= start_time <= time(20, 30):
                time_ranges['19:31-20:30'] += 1
            elif time(20, 31) <= start_time <= time(21, 30):
                time_ranges['20:31-21:30'] += 1
            else:
                time_ranges['21:31以后'] += 1
        except:
            continue
    
    for time_range, count in time_ranges.items():
        if count > 0:
            print(f"  • {time_range}: {count}个作业")
    
    # 超时作业详情
    print(f"\n📋 超时作业详情:")
    print("日期     栋别  开始时间  结束时间  作业类型  作业内容")
    print("-" * 60)
    
    for _, job in overtime_jobs.iterrows():
        print(f"{job['日期']:<8} {job['栋别']:<4} {job['起始时间']:<8} {job['截止时间']:<8} {job['作业类型']:<6} {job['作业内容']}")
    
    # 改善建议
    print(f"\n💡 改善建议:")
    
    if overtime_rate > 10:
        print("  🔴 超时率较高，需要重点关注")
    elif overtime_rate > 5:
        print("  🟡 超时率中等，建议优化")
    else:
        print("  🟢 超时率较低，保持现状")
    
    # 具体建议
    c_building_rate = building_stats.loc['C', '超时率%'] if 'C' in building_stats.index else 0
    if c_building_rate > 10:
        print("  • C栋超时率最高，建议优先优化C栋作业安排")
    
    if time_ranges['19:31-20:30'] > 0 or time_ranges['20:31-21:30'] > 0:
        print("  • 存在19:30后开始的作业，建议调整作业时间安排")
    
    if 'US001' in overtime_jobs['作业内容'].values:
        print("  • US001机种超时较多，建议检查该机种作业流程")
    
    print(f"\n📊 数据已更新到HTML报告中，请刷新浏览器查看可视化图表")

if __name__ == "__main__":
    generate_detailed_overtime_report()
