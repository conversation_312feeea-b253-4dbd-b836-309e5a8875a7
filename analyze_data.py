import pandas as pd
import numpy as np
from datetime import datetime
import json

# 读取Excel文件
df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')

print('=== 数据基本信息 ===')
print(f'总行数: {len(df)}')
print(f'总列数: {len(df.columns)}')

print('\n=== 列名 ===')
for i, col in enumerate(df.columns):
    print(f'{i}: {col}')

print('\n=== 日期分布 ===')
date_counts = df['日期'].value_counts().sort_index()
print(date_counts)

print('\n=== 栋别分布 ===')
building_counts = df['栋别'].value_counts()
print(building_counts)

print('\n=== 作业类型分布 ===')
work_type_counts = df['作业类型'].value_counts()
print(work_type_counts)

print('\n=== 方向分布 ===')
if '方向' in df.columns:
    direction_counts = df['方向'].value_counts()
    print(direction_counts)

print('\n=== 数量统计 ===')
print(f'总数量: {df["数量"].sum()}')
print(f'平均数量: {df["数量"].mean():.2f}')
print(f'最大数量: {df["数量"].max()}')
print(f'最小数量: {df["数量"].min()}')

print('\n=== 实际数据完整性检查 ===')
print(f'有实际开始时间的记录: {df["实际开始时间"].notna().sum()}')
print(f'有实际结束时间的记录: {df["实际结束时间"].notna().sum()}')
print(f'有实际作业时长的记录: {df["实际作业时长"].notna().sum()}')
print(f'有异常原因的记录: {df["异常原因"].notna().sum()}')

print('\n=== 样本数据 ===')
print(df[['日期', '栋别', '起始时间', '截止时间', '作业类型', '数量']].head(10))

# 按日期和栋别统计
print('\n=== 按日期和栋别统计 ===')
daily_building_stats = df.groupby(['日期', '栋别']).agg({
    '数量': ['count', 'sum'],
    '作业时长': 'count'
}).round(2)
print(daily_building_stats.head(20))

# 保存分析结果到JSON文件
analysis_result = {
    'total_records': len(df),
    'date_distribution': date_counts.to_dict(),
    'building_distribution': building_counts.to_dict(),
    'work_type_distribution': work_type_counts.to_dict(),
    'total_quantity': int(df["数量"].sum()),
    'avg_quantity': float(df["数量"].mean()),
    'has_actual_data': {
        'start_time': int(df["实际开始时间"].notna().sum()),
        'end_time': int(df["实际结束时间"].notna().sum()),
        'duration': int(df["实际作业时长"].notna().sum()),
        'exception': int(df["异常原因"].notna().sum())
    }
}

with open('data_analysis.json', 'w', encoding='utf-8') as f:
    json.dump(analysis_result, f, ensure_ascii=False, indent=2)

print('\n分析结果已保存到 data_analysis.json')
