import pandas as pd
from datetime import datetime, time

def final_verification_report():
    """最终验证报告 - 确保所有数据正确"""
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    print("=" * 70)
    print("📊 8月份作业日历热力图与超时分析报告 - 最终验证")
    print("=" * 70)
    
    # 基础统计
    total_tasks = len(df)
    working_days = df['日期'].nunique()
    daily_counts = df.groupby('日期').size()
    max_daily_tasks = daily_counts.max()
    min_daily_tasks = daily_counts.min()
    avg_daily_tasks = daily_counts.mean()
    
    # 超时分析
    def is_overtime(start_time_str):
        if pd.isna(start_time_str):
            return False
        try:
            time_str = str(start_time_str).strip()
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])
            start_time = datetime.strptime(time_str, '%H:%M').time()
            return start_time > time(17, 30)
        except:
            return False
    
    df['是否超时'] = df['起始时间'].apply(is_overtime)
    overtime_tasks = df['是否超时'].sum()
    overtime_rate = (overtime_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    # 栋别统计
    building_stats = df.groupby('栋别').size()
    
    print("📈 Excel数据验证:")
    print(f"  ✓ 总作业数: {total_tasks}")
    print(f"  ✓ 工作天数: {working_days}")
    print(f"  ✓ A栋: {building_stats.get('A', 0)}个作业")
    print(f"  ✓ B栋: {building_stats.get('B', 0)}个作业")
    print(f"  ✓ C栋: {building_stats.get('C', 0)}个作业")
    
    print(f"\n📊 作业强度分析:")
    print(f"  ✓ 最高强度: {max_daily_tasks}个任务/天")
    print(f"  ✓ 最低强度: {min_daily_tasks}个任务/天")
    print(f"  ✓ 平均强度: {avg_daily_tasks:.1f}个任务/天")
    
    # 找出最高强度的日期
    max_day = daily_counts.idxmax()
    print(f"  ✓ 最高强度日期: {max_day} ({max_daily_tasks}个任务)")
    
    print(f"\n⏰ 超时分析 (开始时间>17:30):")
    print(f"  ✓ 超时作业数: {overtime_tasks}")
    print(f"  ✓ 超时率: {overtime_rate:.1f}%")
    
    # 验证HTML报告内容
    print(f"\n📄 HTML报告验证:")
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 检查关键数据
    checks = [
        (f"17个任务/天", "最高强度"),
        (f"5.3%", "超时率"),
        (f"9.2个任务", "平均强度"),
        (f"totalTasks: 285", "JavaScript总任务数"),
        (f"maxDailyTasks: 17", "JavaScript最大日任务数"),
        (f"overtimeTasks: 15", "JavaScript超时任务数"),
        (f"overtimeRate: 5.3", "JavaScript超时率"),
        (f"avgDailyTasks: 9.2", "JavaScript平均任务数")
    ]
    
    all_correct = True
    for check_text, description in checks:
        if check_text in html_content:
            print(f"  ✓ {description}: {check_text}")
        else:
            print(f"  ✗ {description}: 未找到 {check_text}")
            all_correct = False
    
    # 每日作业分布详情
    print(f"\n📅 每日作业分布 (前10天):")
    daily_sorted = daily_counts.sort_values(ascending=False)
    for i, (date, count) in enumerate(daily_sorted.head(10).items()):
        print(f"  {i+1:2d}. {date}: {count}个作业")
    
    # 超时作业详情
    overtime_jobs = df[df['是否超时'] == True]
    if len(overtime_jobs) > 0:
        print(f"\n⚠️  超时作业分布:")
        overtime_by_building = overtime_jobs.groupby('栋别').size()
        for building, count in overtime_by_building.items():
            total_building = building_stats.get(building, 0)
            rate = (count / total_building * 100) if total_building > 0 else 0
            print(f"  • {building}栋: {count}个超时 / {total_building}个总作业 ({rate:.1f}%)")
    
    print(f"\n✅ 验证结果:")
    if all_correct:
        print("  🎉 所有数据已正确更新！")
        print("  📊 HTML报告中的统计数据与Excel数据完全一致")
        print("  🔄 请刷新浏览器查看最新的正确数据")
    else:
        print("  ⚠️  部分数据可能需要进一步检查")
    
    print(f"\n📋 关键修正内容:")
    print(f"  • 最高强度: 19 → {max_daily_tasks}个任务/天")
    print(f"  • 超时率: 8.6% → {overtime_rate:.1f}%")
    print(f"  • 平均强度: 9.0 → {avg_daily_tasks:.1f}个任务/天")
    print(f"  • 基于真实Excel数据重新计算所有指标")

if __name__ == "__main__":
    final_verification_report()
