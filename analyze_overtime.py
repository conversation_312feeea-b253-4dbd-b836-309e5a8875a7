import pandas as pd
import json
import re
from datetime import datetime, time

def analyze_overtime_data():
    """分析超时作业数据：开始作业时间超过17:30即为超时"""
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    print("=== 超时作业分析 ===")
    print("超时标准：开始作业时间超过17:30")
    
    # 处理起始时间，转换为时间格式进行比较
    def is_overtime(start_time_str):
        """判断是否为超时作业"""
        if pd.isna(start_time_str):
            return False
        
        try:
            # 处理时间格式，可能是 "18:30" 或 "18:30:00"
            time_str = str(start_time_str).strip()
            
            # 如果包含秒，去掉秒部分
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])
            
            # 转换为时间对象
            start_time = datetime.strptime(time_str, '%H:%M').time()
            overtime_threshold = time(17, 30)  # 17:30
            
            return start_time > overtime_threshold
        except:
            return False
    
    # 添加超时标记列
    df['是否超时'] = df['起始时间'].apply(is_overtime)
    
    # 统计超时数据
    total_tasks = len(df)
    overtime_tasks = df['是否超时'].sum()
    overtime_rate = (overtime_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    print(f"总作业数: {total_tasks}")
    print(f"超时作业数: {overtime_tasks}")
    print(f"超时率: {overtime_rate:.1f}%")
    
    # 按日期统计超时情况
    print("\n=== 每日超时统计 ===")
    daily_overtime = df.groupby('日期').agg({
        '是否超时': ['count', 'sum']
    }).round(1)
    daily_overtime.columns = ['总作业数', '超时作业数']
    daily_overtime['超时率%'] = (daily_overtime['超时作业数'] / daily_overtime['总作业数'] * 100).round(1)
    
    print(daily_overtime)
    
    # 按栋别统计超时情况
    print("\n=== 各栋超时统计 ===")
    building_overtime = df.groupby('栋别').agg({
        '是否超时': ['count', 'sum']
    }).round(1)
    building_overtime.columns = ['总作业数', '超时作业数']
    building_overtime['超时率%'] = (building_overtime['超时作业数'] / building_overtime['总作业数'] * 100).round(1)
    
    print(building_overtime)
    
    # 生成每日超时趋势数据（用于图表）
    overtime_trend_data = []
    for day in range(1, 32):
        date_str = f"8/{day}"
        day_data = df[df['日期'] == date_str]
        overtime_count = day_data['是否超时'].sum()
        overtime_trend_data.append([f"2025-08-{day:02d}", int(overtime_count)])
    
    print(f"\n=== 超时趋势数据 ===")
    print("日期 -> 超时作业数")
    for item in overtime_trend_data:
        if item[1] > 0:
            print(f"{item[0]} -> {item[1]}")
    
    # 查看具体的超时作业
    print(f"\n=== 超时作业详情 ===")
    overtime_jobs = df[df['是否超时'] == True][['日期', '栋别', '起始时间', '截止时间', '作业类型', '作业内容', '数量']]
    print(f"超时作业详情（前20条）:")
    print(overtime_jobs.head(20))
    
    return {
        'total_tasks': total_tasks,
        'overtime_tasks': int(overtime_tasks),
        'overtime_rate': round(overtime_rate, 1),
        'overtime_trend_data': overtime_trend_data,
        'daily_stats': daily_overtime.to_dict(),
        'building_stats': building_overtime.to_dict()
    }

def update_html_with_overtime_data(overtime_data):
    """更新HTML报告中的超时数据"""
    
    print("\n=== 更新HTML报告 ===")
    
    # 读取HTML文件
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 更新统计数据
    html_content = re.sub(
        r'<div class="stat-number">0</div>\s*<div class="stat-label">超时作业任务数</div>',
        f'<div class="stat-number">{overtime_data["overtime_tasks"]}</div>\n                <div class="stat-label">超时作业任务数</div>',
        html_content
    )
    
    html_content = re.sub(
        r'<div class="stat-number">0%</div>\s*<div class="stat-label">超时作业比例</div>',
        f'<div class="stat-number">{overtime_data["overtime_rate"]}%</div>\n                <div class="stat-label">超时作业比例</div>',
        html_content
    )
    
    # 更新JavaScript中的统计数据
    html_content = re.sub(r'overtimeTasks: \d+', f'overtimeTasks: {overtime_data["overtime_tasks"]}', html_content)
    html_content = re.sub(r'overtimeRate: [\d.]+', f'overtimeRate: {overtime_data["overtime_rate"]}', html_content)
    
    # 更新超时趋势数据
    overtime_trend_str = json.dumps(overtime_data['overtime_trend_data'])
    html_content = re.sub(
        r'const overtimeTrendData = \[.*?\];',
        f'const overtimeTrendData = {overtime_trend_str};',
        html_content,
        flags=re.DOTALL
    )
    
    # 保存更新后的HTML文件
    with open('8月份作业日历热力图与超时分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✓ HTML报告已更新")
    print(f"  - 超时作业数: {overtime_data['overtime_tasks']}")
    print(f"  - 超时率: {overtime_data['overtime_rate']}%")
    print(f"  - 超时趋势数据已更新")

def main():
    print("开始分析超时作业数据...")
    print("超时标准：开始作业时间 > 17:30")
    
    # 分析超时数据
    overtime_data = analyze_overtime_data()
    
    # 更新HTML报告
    update_html_with_overtime_data(overtime_data)
    
    print(f"\n=== 分析完成 ===")
    print(f"总作业数: {overtime_data['total_tasks']}")
    print(f"超时作业数: {overtime_data['overtime_tasks']}")
    print(f"超时率: {overtime_data['overtime_rate']}%")
    
    if overtime_data['overtime_tasks'] > 0:
        print("\n超时作业主要集中在:")
        # 显示有超时的日期
        for date_data in overtime_data['overtime_trend_data']:
            if date_data[1] > 0:
                print(f"  {date_data[0]}: {date_data[1]}个超时作业")

if __name__ == "__main__":
    main()
