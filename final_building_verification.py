import pandas as pd

def final_building_verification():
    """最终验证栋别数据修正结果"""
    
    print("=" * 60)
    print("📊 栋别作业数据修正验证报告")
    print("=" * 60)
    
    # 从Excel验证实际数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    building_counts = df.groupby('栋别').size()
    total_tasks = len(df)
    
    print("📋 Excel数据验证:")
    for building in ['A', 'B', 'C']:
        count = building_counts.get(building, 0)
        percentage = (count / total_tasks * 100) if total_tasks > 0 else 0
        print(f"  ✓ {building}栋: {count}个作业 ({percentage:.1f}%)")
    
    print(f"  ✓ 总计: {total_tasks}个作业")
    
    # 验证HTML报告内容
    print(f"\n📄 HTML报告验证:")
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 检查栋别卡片数据
    building_checks = [
        ('<div class="number">28</div>', 'A栋作业数'),
        ('<div class="number">171</div>', 'B栋作业数'),
        ('<div class="number">86</div>', 'C栋作业数')
    ]
    
    for check_text, description in building_checks:
        if check_text in html_content:
            print(f"  ✓ {description}: 正确")
        else:
            print(f"  ✗ {description}: 未找到或不正确")
    
    # 检查百分比数据
    percentage_checks = [
        ('B栋承担60.0%的作业量', '栋别百分比分析'),
        ('C栋30.2%', 'C栋百分比'),
        ('A栋9.8%', 'A栋百分比')
    ]
    
    for check_text, description in percentage_checks:
        if check_text in html_content:
            print(f"  ✓ {description}: 正确")
        else:
            print(f"  ✗ {description}: 未找到或不正确")
    
    # 检查其他相关数据
    other_checks = [
        ('最高日作业量17个', '最高强度'),
        ('平均日作业量9.2个', '平均强度'),
        ('超时率5.3%', '超时率'),
        ('共31个工作日', '工作天数')
    ]
    
    for check_text, description in other_checks:
        if check_text in html_content:
            print(f"  ✓ {description}: 正确")
        else:
            print(f"  ✗ {description}: 未找到或不正确")
    
    print(f"\n✅ 修正总结:")
    print(f"  • A栋: 28个作业 (9.8%) - 保持不变 ✓")
    print(f"  • B栋: 156 → 171个作业 (60.0%) - 已修正 ✓")
    print(f"  • C栋: 85 → 86个作业 (30.2%) - 已修正 ✓")
    
    print(f"\n📊 栋别分析:")
    print(f"  • B栋承担最多作业 (60.0%)，是主要作业栋")
    print(f"  • C栋承担适中作业 (30.2%)，作业量合理")
    print(f"  • A栋承担较少作业 (9.8%)，可考虑增加负荷")
    
    print(f"\n🔄 请刷新浏览器查看修正后的正确数据！")

if __name__ == "__main__":
    final_building_verification()
