import pandas as pd
import json
import re

def create_simplified_chart_report():
    """创建一个简化的图表报告，确保Chart.js能正常工作"""
    
    # 读取Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    # 提取机种信息
    def extract_model(content):
        if pd.isna(content):
            return "其他"
        patterns = [r'(JT\d{3})', r'(JH\d{3})', r'(UF\d{3})', r'(US\d{3})']
        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
        return "其他"
    
    df['机种'] = df['作业内容'].apply(extract_model)
    
    # 生成简化的数据
    models = ['JT026', 'JT028', 'JH027']  # 只选择主要的3个机种
    chart_data = {}
    
    for model in models:
        model_df = df[df['机种'] == model]
        
        # 生成每日数据
        daily_plan = []
        daily_empty = []
        dates = []
        
        for day in range(1, 32):
            date_str = f"8/{day}"
            dates.append(f"08-{day:02d}")
            
            day_data = model_df[model_df['日期'] == date_str]
            
            # 计划数量（所有作业）
            plan_qty = day_data['数量'].sum() / 1000
            daily_plan.append(round(plan_qty, 1))
            
            # 空箱数量（入库作业）
            empty_qty = day_data[day_data['作业类型'] == '入库']['数量'].sum() / 1000
            daily_empty.append(round(empty_qty, 1))
        
        chart_data[model] = {
            "dates": dates,
            "plan": daily_plan,
            "empty_box": daily_empty,
            "working_days": sum(1 for x in daily_plan if x > 0),
            "plan_total": round(sum(daily_plan), 1),
            "empty_total": round(sum(daily_empty), 1)
        }
    
    # 创建简化的HTML报告
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8月空箱纳入vs生产计划每日对比分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }}
        .model-selector {{
            margin-bottom: 20px;
        }}
        .model-selector select {{
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 18px;
        }}
        .summary-card .value {{
            font-size: 24px;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>8月空箱纳入vs生产计划每日对比分析报告</h1>
            <p>基于作业登记表ABC栋20250801~20250831_自动排程.xlsx数据 | 分析时间范围: 2025年8月1日-31日</p>
        </div>

        <div class="summary-grid" id="summaryGrid">
            <!-- 概览卡片将通过JavaScript生成 -->
        </div>

        <div class="chart-container">
            <div class="model-selector">
                <label for="modelSelect">选择机种: </label>
                <select id="modelSelect" onchange="updateChart()">
                    <!-- 选项将通过JavaScript生成 -->
                </select>
            </div>
            <canvas id="comparisonChart" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        // 数据
        const emptyBoxAnalysisData = {json.dumps(chart_data, ensure_ascii=False, indent=12)};
        
        let comparisonChart = null;

        // 生成概览卡片
        function generateSummaryCards() {{
            const summaryGrid = document.getElementById('summaryGrid');
            summaryGrid.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {{
                const data = emptyBoxAnalysisData[model];
                
                const card = document.createElement('div');
                card.className = 'summary-card';
                card.innerHTML = `
                    <h3>${{model}}</h3>
                    <div class="value">${{data.working_days}}天</div>
                    <div>工作天数</div>
                    <div style="margin-top: 10px;">
                        <div>计划总量: ${{data.plan_total}}K</div>
                        <div>空箱总量: ${{data.empty_total}}K</div>
                    </div>
                `;
                summaryGrid.appendChild(card);
            }});
        }}

        // 填充机种选择器
        function populateModelSelector() {{
            const select = document.getElementById('modelSelect');
            select.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {{
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            }});
        }}

        // 更新图表
        function updateChart() {{
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel || !emptyBoxAnalysisData[selectedModel]) return;

            const data = emptyBoxAnalysisData[selectedModel];
            const ctx = document.getElementById('comparisonChart').getContext('2d');

            if (comparisonChart) {{
                comparisonChart.destroy();
            }}

            comparisonChart = new Chart(ctx, {{
                type: 'line',
                data: {{
                    labels: data.dates,
                    datasets: [{{
                        label: '生产计划 (K件)',
                        data: data.plan,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.1
                    }}, {{
                        label: '空箱纳入 (K件)',
                        data: data.empty_box,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.1
                    }}]
                }},
                options: {{
                    responsive: true,
                    plugins: {{
                        title: {{
                            display: true,
                            text: `${{selectedModel}} - 每日计划vs空箱纳入对比`
                        }},
                        legend: {{
                            display: true
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '数量 (千件)'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: '日期'
                            }}
                        }}
                    }}
                }}
            }});
        }}

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('页面加载完成，Chart.js版本:', Chart.version);
            generateSummaryCards();
            populateModelSelector();
            updateChart();
        }});

        // 错误处理
        window.addEventListener('error', function(e) {{
            console.error('JavaScript错误:', e.error);
        }});
    </script>
</body>
</html>'''

    # 保存新的HTML文件
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✓ 已创建简化版图表报告")
    print(f"✓ 包含机种: {list(chart_data.keys())}")
    for model, data in chart_data.items():
        print(f"  - {model}: {data['working_days']}工作天, 计划总量{data['plan_total']}K, 空箱总量{data['empty_total']}K")

if __name__ == "__main__":
    create_simplified_chart_report()
