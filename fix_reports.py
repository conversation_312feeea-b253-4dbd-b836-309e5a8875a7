import pandas as pd
import json
import re
from datetime import datetime

def fix_first_report():
    """修复第一个HTML报告的统计数据显示问题"""
    print("修复第一个HTML报告...")
    
    # 读取Excel数据重新计算统计
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    total_tasks = len(df)
    working_days = df['日期'].nunique()
    
    # 读取HTML文件
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 修复硬编码的统计数字
    html_content = re.sub(r'<div class="stat-number">269</div>', 
                         f'<div class="stat-number">{total_tasks}</div>', 
                         html_content)
    
    # 更新工作天数
    html_content = re.sub(r'<div class="stat-number">30</div>', 
                         f'<div class="stat-number">{working_days}</div>', 
                         html_content)
    
    # 更新statsData中的数据
    html_content = re.sub(r'totalTasks: \d+', f'totalTasks: {total_tasks}', html_content)
    html_content = re.sub(r'workingDays: \d+', f'workingDays: {working_days}', html_content)
    
    # 计算最大日作业数
    daily_counts = df.groupby('日期').size()
    max_daily = daily_counts.max()
    html_content = re.sub(r'maxDailyTasks: \d+', f'maxDailyTasks: {max_daily}', html_content)
    
    # 由于没有实际超时数据，设置超时相关数据为0
    html_content = re.sub(r'<div class="stat-number">23</div>', 
                         '<div class="stat-number">0</div>', html_content)
    html_content = re.sub(r'<div class="stat-number">8\.6%</div>', 
                         '<div class="stat-number">0%</div>', html_content)
    html_content = re.sub(r'overtimeTasks: \d+', 'overtimeTasks: 0', html_content)
    html_content = re.sub(r'overtimeRate: [\d.]+', 'overtimeRate: 0', html_content)
    
    # 清空超时趋势数据（因为没有实际超时数据）
    overtime_data = [f'["{2025}-08-{i:02d}", 0]' for i in range(1, 32)]
    overtime_data_str = '[' + ', '.join(overtime_data) + ']'
    html_content = re.sub(r'const overtimeTrendData = \[.*?\];', 
                         f'const overtimeTrendData = {overtime_data_str};', 
                         html_content, flags=re.DOTALL)
    
    # 保存修复后的文件
    with open('8月份作业日历热力图与超时分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✓ 已修复第一个报告，总任务数: {total_tasks}, 工作天数: {working_days}")

def fix_second_report():
    """修复第二个HTML报告的图表显示问题"""
    print("修复第二个HTML报告...")
    
    # 读取当前HTML文件
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 检查Chart.js版本并更新为稳定版本
    html_content = re.sub(
        r'<script src="https://cdn\.jsdelivr\.net/npm/chart\.js"></script>',
        '<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>',
        html_content
    )
    
    # 确保数据格式正确，添加错误处理
    js_fix = '''
    <script>
        // 添加错误处理和调试信息
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.error);
        });
        
        // 检查Chart.js是否加载成功
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未能正确加载');
            document.body.innerHTML += '<div style="color: red; text-align: center; margin: 20px;">Chart.js加载失败，请检查网络连接</div>';
        } else {
            console.log('Chart.js加载成功，版本:', Chart.version);
        }
    </script>
    '''
    
    # 在</body>前添加调试脚本
    html_content = html_content.replace('</body>', js_fix + '</body>')
    
    # 修复可能的数据格式问题
    # 确保所有数据都是有效的数字
    def fix_data_format(match):
        data_str = match.group(1)
        try:
            # 解析数据并确保格式正确
            data_lines = data_str.split('\n')
            fixed_lines = []
            for line in data_lines:
                line = line.strip()
                if line and not line.startswith('//'):
                    fixed_lines.append(line)
            return 'const emptyBoxAnalysisData = {\n' + '\n'.join(fixed_lines) + '\n        };'
        except:
            return match.group(0)
    
    # 修复数据格式
    html_content = re.sub(
        r'const emptyBoxAnalysisData = \{(.*?)\};',
        fix_data_format,
        html_content,
        flags=re.DOTALL
    )
    
    # 保存修复后的文件
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✓ 已修复第二个报告的Chart.js问题")

def verify_fixes():
    """验证修复结果"""
    print("\n验证修复结果...")
    
    # 验证第一个报告
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html1 = f.read()
    
    if '285' in html1 and '269' not in html1:
        print("✓ 第一个报告统计数据已修复")
    else:
        print("✗ 第一个报告可能仍有问题")
    
    # 验证第二个报告
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'r', encoding='utf-8') as f:
        html2 = f.read()
    
    if 'chart.js@3.9.1' in html2:
        print("✓ 第二个报告Chart.js版本已更新")
    else:
        print("✗ 第二个报告Chart.js可能仍有问题")

def main():
    print("开始修复HTML报告问题...")
    fix_first_report()
    fix_second_report()
    verify_fixes()
    print("\n修复完成！请刷新浏览器页面查看结果。")

if __name__ == "__main__":
    main()
