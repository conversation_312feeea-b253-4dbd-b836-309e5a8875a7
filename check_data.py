import pandas as pd

# 读取Excel文件，检查是否有多个工作表
excel_file = pd.ExcelFile('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
print('工作表列表:', excel_file.sheet_names)

# 读取第一个工作表
df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx', sheet_name=0)
print(f'\n第一个工作表数据:')
print(f'总行数: {len(df)}')
print(f'总列数: {len(df.columns)}')

print('\n栋别统计:')
building_counts = df['栋别'].value_counts()
print(building_counts)
print(f'A栋: {building_counts.get("A", 0)}条')
print(f'B栋: {building_counts.get("B", 0)}条') 
print(f'C栋: {building_counts.get("C", 0)}条')
print(f'总计: {building_counts.sum()}条')

# 检查是否有空值或其他栋别
print(f'\n栋别字段的所有唯一值: {df["栋别"].unique()}')
print(f'栋别字段是否有空值: {df["栋别"].isna().sum()}')

# 检查数据的完整性
print(f'\n数据完整性检查:')
print(f'日期字段空值: {df["日期"].isna().sum()}')
print(f'栋别字段空值: {df["栋别"].isna().sum()}')
print(f'数量字段空值: {df["数量"].isna().sum()}')

# 显示前几行和后几行数据
print(f'\n前5行数据:')
print(df[['日期', '栋别', '作业类型', '数量']].head())
print(f'\n后5行数据:')
print(df[['日期', '栋别', '作业类型', '数量']].tail())

# 检查是否有其他工作表
if len(excel_file.sheet_names) > 1:
    print(f'\n发现多个工作表，检查其他工作表:')
    for i, sheet_name in enumerate(excel_file.sheet_names):
        if i > 0:  # 跳过第一个已经检查的工作表
            print(f'\n工作表: {sheet_name}')
            try:
                df_other = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx', sheet_name=sheet_name)
                print(f'  行数: {len(df_other)}')
                if '栋别' in df_other.columns:
                    print(f'  栋别分布: {dict(df_other["栋别"].value_counts())}')
            except Exception as e:
                print(f'  读取错误: {e}')

# 按日期统计，确保没有遗漏
print(f'\n按日期统计:')
date_stats = df.groupby(['日期', '栋别']).size().reset_index(name='count')
print(date_stats.head(20))

# 检查日期范围
print(f'\n日期范围检查:')
print(f'最早日期: {df["日期"].min()}')
print(f'最晚日期: {df["日期"].max()}')
print(f'唯一日期数: {df["日期"].nunique()}')
print(f'所有日期: {sorted(df["日期"].unique())}')
