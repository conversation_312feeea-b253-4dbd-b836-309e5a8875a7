def calculate_building_percentages():
    """计算各栋别的正确百分比"""
    
    # 正确的栋别数据
    a_building = 28
    b_building = 171
    c_building = 86
    total = a_building + b_building + c_building
    
    print(f"栋别作业数据:")
    print(f"A栋: {a_building}个作业")
    print(f"B栋: {b_building}个作业")
    print(f"C栋: {c_building}个作业")
    print(f"总计: {total}个作业")
    
    # 计算百分比
    a_percent = (a_building / total) * 100
    b_percent = (b_building / total) * 100
    c_percent = (c_building / total) * 100
    
    print(f"\n栋别百分比:")
    print(f"A栋: {a_percent:.1f}%")
    print(f"B栋: {b_percent:.1f}%")
    print(f"C栋: {c_percent:.1f}%")
    
    print(f"\n用于HTML更新的文本:")
    print(f"B栋承担{b_percent:.1f}%的作业量，C栋{c_percent:.1f}%，A栋{a_percent:.1f}%，分工明确")

if __name__ == "__main__":
    calculate_building_percentages()
