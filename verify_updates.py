import pandas as pd
import json
import re

def verify_html_updates():
    """验证HTML文件更新情况"""
    print("=== 验证HTML报告更新情况 ===\n")
    
    # 验证第一个报告
    print("1. 8月份作业日历热力图与超时分析报告.html")
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html1_content = f.read()
    
    # 提取日历数据
    calendar_match = re.search(r'const calendarData = (\[.*?\]);', html1_content, re.DOTALL)
    if calendar_match:
        calendar_data = eval(calendar_match.group(1))
        total_tasks = sum(item[1] for item in calendar_data)
        print(f"   ✓ 日历数据已更新，总作业数: {total_tasks}")
        print(f"   ✓ 数据点数: {len(calendar_data)}")
        print(f"   ✓ 日期范围: {calendar_data[0][0]} 到 {calendar_data[-1][0]}")
    else:
        print("   ✗ 日历数据未找到")
    
    # 验证栋别数据
    for building in ['A', 'B', 'C']:
        pattern = f'const calendarData{building} = (\\[.*?\\]);'
        match = re.search(pattern, html1_content, re.DOTALL)
        if match:
            data = eval(match.group(1))
            total = sum(item[1] for item in data)
            print(f"   ✓ {building}栋数据已更新，总作业数: {total}")
        else:
            print(f"   ✗ {building}栋数据未找到")
    
    print("\n2. 8月空箱纳入vs生产计划每日对比分析报告.html")
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'r', encoding='utf-8') as f:
        html2_content = f.read()
    
    # 提取机种分析数据
    analysis_match = re.search(r'const emptyBoxAnalysisData = (\{.*?\});', html2_content, re.DOTALL)
    if analysis_match:
        try:
            # 简化的数据提取，只检查结构
            if '"JT026"' in analysis_match.group(1):
                print("   ✓ 机种分析数据已更新")
                
                # 计算包含的机种数量
                models = re.findall(r'"([A-Z]{2}\d{3})":', analysis_match.group(1))
                print(f"   ✓ 包含机种: {', '.join(models)}")
                print(f"   ✓ 机种数量: {len(models)}")
            else:
                print("   ✗ 机种数据格式异常")
        except Exception as e:
            print(f"   ✗ 数据解析错误: {e}")
    else:
        print("   ✗ 机种分析数据未找到")

def generate_summary_report():
    """生成更新摘要报告"""
    print("\n=== 更新摘要报告 ===\n")
    
    # 读取原始Excel数据
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    print("数据源信息:")
    print(f"  • 文件: 作业登记表ABC栋20250801~20250831_自动排程.xlsx")
    print(f"  • 总记录数: {len(df)}")
    print(f"  • 日期范围: {df['日期'].min()} 到 {df['日期'].max()}")
    print(f"  • 栋别分布: {dict(df['栋别'].value_counts())}")
    print(f"  • 作业类型: {dict(df['作业类型'].value_counts())}")
    print(f"  • 总数量: {df['数量'].sum()}")
    
    # 机种统计
    df['机种'] = df['作业内容'].apply(lambda x: x.split('-')[0] if pd.notna(x) and '-' in x else x[:5] if pd.notna(x) else '其他')
    model_stats = df['机种'].value_counts()
    print(f"  • 机种分布: {dict(model_stats.head(10))}")
    
    print("\n更新内容:")
    print("  ✓ 8月份作业日历热力图与超时分析报告.html")
    print("    - 更新了整体日历热力图数据")
    print("    - 更新了A、B、C栋分别的热力图数据")
    print("    - 更新了总作业数统计")
    
    print("  ✓ 8月空箱纳入vs生产计划每日对比分析报告.html")
    print("    - 更新了各机种的每日计划数据")
    print("    - 更新了空箱纳入数据（基于入库作业）")
    print("    - 更新了计划vs实际的差异分析")
    
    print("\n注意事项:")
    print("  • 由于Excel中实际执行数据为空，超时分析基于计划数据")
    print("  • 空箱纳入数据基于入库作业类型进行估算")
    print("  • 数量单位已转换为千件便于显示")

def main():
    verify_html_updates()
    generate_summary_report()
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    main()
