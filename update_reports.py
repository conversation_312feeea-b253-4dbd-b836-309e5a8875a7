import pandas as pd
import json
import re
from datetime import datetime

def extract_model_from_content(content):
    """从作业内容中提取机种代码"""
    if pd.isna(content):
        return "其他"
    
    # 提取机种代码模式
    patterns = [
        r'(JT\d{3})',
        r'(JH\d{3})',
        r'(UF\d{3})',
        r'(US\d{3})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, content)
        if match:
            return match.group(1)
    
    return "其他"

def process_excel_data():
    """处理Excel数据"""
    # 读取Excel文件
    df = pd.read_excel('作业登记表ABC栋20250801~20250831_自动排程.xlsx')
    
    # 转换日期格式
    df['标准日期'] = df['日期'].apply(lambda x: f"2025-08-{x.split('/')[1].zfill(2)}")
    
    # 提取机种
    df['机种'] = df['作业内容'].apply(extract_model_from_content)
    
    return df

def generate_calendar_data(df):
    """生成日历热力图数据"""
    # 总体数据
    daily_total = df.groupby('标准日期')['数量'].count().reset_index()
    calendar_data = [[row['标准日期'], int(row['数量'])] for _, row in daily_total.iterrows()]
    
    # 按栋别分组
    calendar_data_a = []
    calendar_data_b = []
    calendar_data_c = []
    
    # 生成8月1日到31日的完整日期列表
    for day in range(1, 32):
        date_str = f"2025-08-{day:02d}"
        
        # A栋数据
        a_count = len(df[(df['标准日期'] == date_str) & (df['栋别'] == 'A')])
        calendar_data_a.append([date_str, a_count])
        
        # B栋数据
        b_count = len(df[(df['标准日期'] == date_str) & (df['栋别'] == 'B')])
        calendar_data_b.append([date_str, b_count])
        
        # C栋数据
        c_count = len(df[(df['标准日期'] == date_str) & (df['栋别'] == 'C')])
        calendar_data_c.append([date_str, c_count])
    
    # 生成总体数据（确保包含所有日期）
    calendar_data_full = []
    for day in range(1, 32):
        date_str = f"2025-08-{day:02d}"
        total_count = len(df[df['标准日期'] == date_str])
        calendar_data_full.append([date_str, total_count])
    
    return {
        'calendar_data': calendar_data_full,
        'calendar_data_a': calendar_data_a,
        'calendar_data_b': calendar_data_b,
        'calendar_data_c': calendar_data_c
    }

def generate_model_analysis_data(df):
    """生成机种分析数据"""
    models = df['机种'].unique()
    analysis_data = {}
    
    for model in models:
        if model == "其他":
            continue
            
        model_df = df[df['机种'] == model]
        
        # 生成日期列表
        dates = [f"08-{i:02d}" for i in range(1, 32)]
        
        # 计算每日计划数量（基于数量字段）
        daily_plan = []
        daily_empty_box = []  # 这里用入库作为空箱纳入的近似
        
        for day in range(1, 32):
            date_str = f"8/{day}"
            day_data = model_df[model_df['日期'] == date_str]
            
            # 计划数量（所有作业的数量总和）
            plan_qty = day_data['数量'].sum() / 1000  # 转换为千件
            daily_plan.append(round(plan_qty, 1))
            
            # 空箱纳入（入库作业的数量）
            empty_box_qty = day_data[day_data['作业类型'] == '入库']['数量'].sum() / 1000
            daily_empty_box.append(round(empty_box_qty, 1))
        
        # 计算差异
        daily_diff = [round(empty - plan, 2) for empty, plan in zip(daily_empty_box, daily_plan)]
        
        # 计算统计数据
        working_days = sum(1 for x in daily_plan if x > 0)
        plan_total = sum(daily_plan)
        empty_total = sum(daily_empty_box)
        
        analysis_data[model] = {
            "dates": dates,
            "plan": daily_plan,
            "empty_box": daily_empty_box,
            "daily_diff": daily_diff,
            "working_days": working_days,
            "plan_total": round(plan_total, 1),
            "empty_total": round(empty_total, 1),
            "avg_daily_plan": round(plan_total / max(working_days, 1), 2),
            "avg_daily_empty": round(empty_total / max(working_days, 1), 2)
        }
    
    return analysis_data

def update_html_reports(calendar_data, model_analysis_data):
    """更新HTML报告"""
    
    # 更新第一个报告
    with open('8月份作业日历热力图与超时分析报告.html', 'r', encoding='utf-8') as f:
        html1_content = f.read()
    
    # 替换日历数据
    html1_content = re.sub(
        r'const calendarData = \[.*?\];',
        f'const calendarData = {json.dumps(calendar_data["calendar_data"])};',
        html1_content,
        flags=re.DOTALL
    )
    
    html1_content = re.sub(
        r'const calendarDataA = \[.*?\];',
        f'const calendarDataA = {json.dumps(calendar_data["calendar_data_a"])};',
        html1_content,
        flags=re.DOTALL
    )
    
    html1_content = re.sub(
        r'const calendarDataB = \[.*?\];',
        f'const calendarDataB = {json.dumps(calendar_data["calendar_data_b"])};',
        html1_content,
        flags=re.DOTALL
    )
    
    html1_content = re.sub(
        r'const calendarDataC = \[.*?\];',
        f'const calendarDataC = {json.dumps(calendar_data["calendar_data_c"])};',
        html1_content,
        flags=re.DOTALL
    )
    
    # 更新统计数据
    total_tasks = sum(item[1] for item in calendar_data["calendar_data"])
    html1_content = re.sub(
        r'totalTasks: \d+',
        f'totalTasks: {total_tasks}',
        html1_content
    )
    
    # 保存第一个报告
    with open('8月份作业日历热力图与超时分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html1_content)
    
    # 更新第二个报告
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'r', encoding='utf-8') as f:
        html2_content = f.read()
    
    # 替换机种分析数据
    html2_content = re.sub(
        r'const emptyBoxAnalysisData = \{.*?\};',
        f'const emptyBoxAnalysisData = {json.dumps(model_analysis_data, ensure_ascii=False, indent=12)};',
        html2_content,
        flags=re.DOTALL
    )
    
    # 保存第二个报告
    with open('8月空箱纳入vs生产计划每日对比分析报告.html', 'w', encoding='utf-8') as f:
        f.write(html2_content)

def main():
    print("开始处理Excel数据...")
    df = process_excel_data()
    
    print("生成日历热力图数据...")
    calendar_data = generate_calendar_data(df)
    
    print("生成机种分析数据...")
    model_analysis_data = generate_model_analysis_data(df)
    
    print("更新HTML报告...")
    update_html_reports(calendar_data, model_analysis_data)
    
    print("更新完成！")
    
    # 输出统计信息
    print(f"\n统计信息:")
    print(f"总作业记录数: {len(df)}")
    print(f"涉及机种: {list(model_analysis_data.keys())}")
    print(f"日历数据点数: {len(calendar_data['calendar_data'])}")

if __name__ == "__main__":
    main()
